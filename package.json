{"name": "mev-arbitrage-bot", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "next dev -p 3000", "dev:backend": "node working-backend.mjs", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "next build", "build:backend": "tsc -p backend/tsconfig.json", "start": "tsx startup.ts", "start:production": "NODE_ENV=production tsx startup.ts", "start:backend": "node working-backend.mjs", "start:full": "node start-full-system.mjs", "start:enhanced": "node enhanced-backend.mjs", "start:enhanced-system": "node start-enhanced-system.mjs", "validate:system": "tsx scripts/validate-system-integration.ts", "start:ml": "tsx startup.ts", "setup:db": "tsx scripts/setup-ml-database.js", "verify": "tsx scripts/verify-system.ts", "verify:system": "tsx scripts/verify-system.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:contracts": "hardhat test", "test:integration": "jest --testPathPattern=tests/integration", "test:performance": "jest --testPathPattern=tests/performance", "test:e2e": "jest --testPathPattern=tests/e2e", "test:enhanced": "jest tests/integration/enhanced-system-integration.test.ts", "test:workflow": "jest tests/e2e/complete-arbitrage-workflow.test.ts", "test:benchmark": "jest tests/performance/enhanced-benchmark.test.ts", "test:all": "npm run test:contracts && npm run test && npm run test:integration && npm run test:performance", "test:comprehensive": "tsx scripts/run-integration-tests.ts", "demo:integration": "tsx scripts/demo-system-integration.ts", "compile:contracts": "hardhat compile", "deploy:contracts": "hardhat run scripts/deploy.ts", "benchmark": "node tests/performance/benchmark.js"}, "dependencies": {"@google/genai": "^1.1.0", "@influxdata/influxdb-client": "^1.35.0", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.17.0", "@sentry/nextjs": "^7.99.0", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "chart.js": "^4.4.1", "chartjs-adapter-date-fns": "^3.0.0", "clsx": "^2.1.0", "cors": "^2.8.5", "date-fns": "^3.2.0", "decimal.js": "^10.4.3", "dotenv": "^16.5.0", "ethers": "^6.8.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "framer-motion": "^10.18.0", "lodash": "^4.17.21", "lucide-react": "^0.312.0", "next": "^14.1.0", "next-themes": "^0.2.1", "node-cron": "^3.0.3", "postcss": "^8.5.3", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-use-websocket": "^4.5.0", "react-window": "^1.8.8", "redis": "^4.6.10", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.1", "uuid": "^9.0.1", "web3": "^4.2.2", "winston": "^3.11.0", "ws": "^8.14.2", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "@openzeppelin/contracts": "^5.0.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/lodash": "^4.14.202", "@types/node": "^22.14.0", "@types/node-cron": "^3.0.11", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-window": "^1.8.8", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "chai": "^4.3.10", "concurrently": "^8.2.2", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "hardhat": "^2.19.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "~5.7.2"}}