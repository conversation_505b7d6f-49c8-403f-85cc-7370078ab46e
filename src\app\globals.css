@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #475569 #1e293b;
}

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variant-numeric: tabular-nums;
}

/* Custom utilities */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent;
  }
  
  .profit-gradient {
    @apply bg-gradient-to-r from-success-400 to-success-600 bg-clip-text text-transparent;
  }
  
  .loss-gradient {
    @apply bg-gradient-to-r from-danger-400 to-danger-600 bg-clip-text text-transparent;
  }
  
  .card-glow {
    @apply shadow-lg shadow-primary-500/10 border border-dark-700/50;
  }
  
  .card-glow-success {
    @apply shadow-lg shadow-success-500/10 border border-success-500/20;
  }
  
  .card-glow-danger {
    @apply shadow-lg shadow-danger-500/10 border border-danger-500/20;
  }
  
  .glass-effect {
    @apply bg-dark-800/80 backdrop-blur-sm border border-dark-700/50;
  }
  
  .trading-grid {
    @apply grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }
  
  .metric-card {
    @apply glass-effect rounded-xl p-6 transition-all duration-200 hover:bg-dark-700/80;
  }
  
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-success {
    @apply status-indicator bg-success-500/20 text-success-400 border border-success-500/30;
  }
  
  .status-warning {
    @apply status-indicator bg-warning-500/20 text-warning-400 border border-warning-500/30;
  }
  
  .status-danger {
    @apply status-indicator bg-danger-500/20 text-danger-400 border border-danger-500/30;
  }
  
  .status-info {
    @apply status-indicator bg-primary-500/20 text-primary-400 border border-primary-500/30;
  }
}

/* Component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .btn-secondary {
    @apply bg-dark-700 hover:bg-dark-600 text-dark-200 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-dark-500 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .input-field {
    @apply bg-dark-800 border border-dark-700 text-dark-100 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200;
  }
  
  .data-table {
    @apply w-full text-sm text-left text-dark-300;
  }
  
  .data-table th {
    @apply px-6 py-3 bg-dark-800 text-xs font-medium text-dark-400 uppercase tracking-wider border-b border-dark-700;
  }
  
  .data-table td {
    @apply px-6 py-4 whitespace-nowrap border-b border-dark-800;
  }
  
  .chart-container {
    @apply bg-dark-800/50 rounded-xl p-4 border border-dark-700/50;
  }
}

/* Animation classes */
.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-subtle {
  animation: bounceSubtle 2s infinite;
}

@keyframes bounceSubtle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-dark-700 rounded;
}

.skeleton-text {
  @apply skeleton h-4 w-full;
}

.skeleton-circle {
  @apply skeleton rounded-full;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
