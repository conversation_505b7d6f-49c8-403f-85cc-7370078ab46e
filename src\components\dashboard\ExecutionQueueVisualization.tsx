'use client';

import { useState, useMemo } from 'react';
import { 
  Clock, 
  Zap, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Play,
  Pause,
  BarChart3,
  Filter,
  <PERSON>Up,
  ArrowDown,
  Timer
} from 'lucide-react';

import { ExecutionQueue } from '@/types';
import { 
  formatCurrency, 
  formatTimeAgo, 
  formatDuration,
  formatNetworkName,
  getStatusColor,
  getPriorityColor,
  NETWORK_COLORS
} from '@/lib/utils';
import { LoadingSpinner, SkeletonTable } from '@/components/ui/LoadingSpinner';

interface ExecutionQueueVisualizationProps {
  queue: ExecutionQueue[];
  isLoading?: boolean;
  detailed?: boolean;
}

interface QueueItemProps {
  item: ExecutionQueue;
  position: number;
  detailed?: boolean;
}

function QueueItem({ item, position, detailed = false }: QueueItemProps) {
  const statusIcon = {
    queued: Clock,
    processing: Play,
    completed: CheckCircle,
    failed: XCircle,
    cancelled: Pause,
  }[item.status];

  const StatusIcon = statusIcon;
  const priorityColor = getPriorityColor(item.priority);
  const statusColor = getStatusColor(item.status);

  return (
    <div className={`
      glass-effect rounded-lg p-4 border-l-4 transition-all duration-200 hover:bg-dark-700/30
      ${item.priority === 'critical' ? 'border-l-error-500' : 
        item.priority === 'high' ? 'border-l-warning-500' : 
        item.priority === 'medium' ? 'border-l-primary-500' : 'border-l-dark-600'}
    `}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className={`
            w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold
            ${item.priority === 'critical' ? 'bg-error-500/20 text-error-400' :
              item.priority === 'high' ? 'bg-warning-500/20 text-warning-400' :
              item.priority === 'medium' ? 'bg-primary-500/20 text-primary-400' :
              'bg-dark-600/20 text-dark-400'}
          `}>
            {position}
          </div>
          
          <div>
            <div className="flex items-center space-x-2">
              <StatusIcon className={`w-4 h-4 ${statusColor}`} />
              <span className="font-medium text-dark-100">
                {item.opportunity_id || item.opportunityId}
              </span>
            </div>
            <div className="text-xs text-dark-400 mt-1">
              Priority: <span className={priorityColor}>{item.priority}</span>
            </div>
          </div>
        </div>

        <div className="text-right">
          <div className="text-lg font-semibold text-success-400">
            {formatCurrency(item.estimated_profit || item.estimatedProfit || 0)}
          </div>
          <div className="text-xs text-dark-400">
            Est. Profit
          </div>
        </div>
      </div>

      {detailed && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 pt-4 border-t border-dark-700">
          <div>
            <div className="text-xs text-dark-500 mb-1">Queue Position</div>
            <div className="text-sm font-medium text-dark-200">
              #{item.queue_position || item.queuePosition || position}
            </div>
          </div>
          
          <div>
            <div className="text-xs text-dark-500 mb-1">Est. Execution</div>
            <div className="text-sm font-medium text-dark-200">
              {formatDuration((item.estimated_execution_time || item.estimatedExecutionTime || 0) * 1000)}
            </div>
          </div>
          
          <div>
            <div className="text-xs text-dark-500 mb-1">Created</div>
            <div className="text-sm font-medium text-dark-200">
              {formatTimeAgo(item.created_at || item.createdAt || '')}
            </div>
          </div>
          
          <div>
            <div className="text-xs text-dark-500 mb-1">Status</div>
            <div className={`text-sm font-medium capitalize ${statusColor}`}>
              {item.status}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export function ExecutionQueueVisualization({ 
  queue, 
  isLoading = false, 
  detailed = false 
}: ExecutionQueueVisualizationProps) {
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('position');

  // Filter and sort queue items
  const filteredQueue = useMemo(() => {
    let filtered = [...queue];

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(item => item.status === filterStatus);
    }

    // Apply priority filter
    if (filterPriority !== 'all') {
      filtered = filtered.filter(item => item.priority === filterPriority);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'position':
          return (a.queue_position || a.queuePosition || 0) - (b.queue_position || b.queuePosition || 0);
        case 'profit':
          return (b.estimated_profit || b.estimatedProfit || 0) - (a.estimated_profit || a.estimatedProfit || 0);
        case 'priority':
          const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        case 'created':
          return new Date(b.created_at || b.createdAt || 0).getTime() - 
                 new Date(a.created_at || a.createdAt || 0).getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [queue, filterStatus, filterPriority, sortBy]);

  // Calculate queue statistics
  const queueStats = useMemo(() => {
    const total = queue.length;
    const byStatus = queue.reduce((acc, item) => {
      acc[item.status] = (acc[item.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const totalProfit = queue.reduce((sum, item) => 
      sum + (item.estimated_profit || item.estimatedProfit || 0), 0);
    
    const avgExecutionTime = queue.length > 0 
      ? queue.reduce((sum, item) => 
          sum + (item.estimated_execution_time || item.estimatedExecutionTime || 0), 0) / queue.length
      : 0;

    return {
      total,
      byStatus,
      totalProfit,
      avgExecutionTime,
      processing: byStatus.processing || 0,
      queued: byStatus.queued || 0,
      completed: byStatus.completed || 0,
      failed: byStatus.failed || 0,
    };
  }, [queue]);

  if (isLoading) {
    return (
      <div className="glass-effect rounded-xl p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-6 h-6 bg-dark-700 rounded animate-pulse"></div>
          <div className="h-6 bg-dark-700 rounded w-32 animate-pulse"></div>
        </div>
        <SkeletonTable rows={5} />
      </div>
    );
  }

  return (
    <div className="glass-effect rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Zap className="w-6 h-6 text-warning-400" />
          <div>
            <h3 className="text-lg font-semibold text-dark-100">
              Execution Queue
            </h3>
            <p className="text-sm text-dark-400">
              {queueStats.total} trades • {queueStats.processing} processing • {queueStats.queued} queued
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-warning-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-dark-400">Live Queue</span>
        </div>
      </div>

      {/* Queue Statistics */}
      {detailed && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="metric-card">
            <div className="flex items-center space-x-2 mb-2">
              <Timer className="w-4 h-4 text-primary-400" />
              <span className="text-sm font-medium text-dark-300">Avg. Execution</span>
            </div>
            <div className="text-xl font-bold text-dark-100">
              {formatDuration(queueStats.avgExecutionTime * 1000)}
            </div>
          </div>

          <div className="metric-card">
            <div className="flex items-center space-x-2 mb-2">
              <BarChart3 className="w-4 h-4 text-success-400" />
              <span className="text-sm font-medium text-dark-300">Total Profit</span>
            </div>
            <div className="text-xl font-bold text-success-400">
              {formatCurrency(queueStats.totalProfit)}
            </div>
          </div>

          <div className="metric-card">
            <div className="flex items-center space-x-2 mb-2">
              <Play className="w-4 h-4 text-warning-400" />
              <span className="text-sm font-medium text-dark-300">Processing</span>
            </div>
            <div className="text-xl font-bold text-warning-400">
              {queueStats.processing}
            </div>
          </div>

          <div className="metric-card">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="w-4 h-4 text-success-400" />
              <span className="text-sm font-medium text-dark-300">Completed</span>
            </div>
            <div className="text-xl font-bold text-success-400">
              {queueStats.completed}
            </div>
          </div>
        </div>
      )}

      {/* Filters and Controls */}
      <div className="flex flex-wrap items-center gap-4 mb-6">
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-dark-400" />
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="input-field text-sm"
          >
            <option value="all">All Status</option>
            <option value="queued">Queued</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <select
          value={filterPriority}
          onChange={(e) => setFilterPriority(e.target.value)}
          className="input-field text-sm"
        >
          <option value="all">All Priority</option>
          <option value="critical">Critical</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>

        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="input-field text-sm"
        >
          <option value="position">Queue Position</option>
          <option value="profit">Estimated Profit</option>
          <option value="priority">Priority</option>
          <option value="created">Created Time</option>
        </select>
      </div>

      {/* Queue Items */}
      <div className="space-y-4">
        {filteredQueue.length === 0 ? (
          <div className="text-center py-12">
            <Clock className="w-12 h-12 text-dark-600 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-dark-300 mb-2">
              No Items in Queue
            </h4>
            <p className="text-dark-500">
              {queue.length === 0
                ? 'The execution queue is currently empty.'
                : 'No items match your current filters.'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredQueue.map((item, index) => (
              <QueueItem
                key={item.id}
                item={item}
                position={index + 1}
                detailed={detailed}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
