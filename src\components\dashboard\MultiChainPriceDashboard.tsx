'use client';

import { useState, useMemo } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  BarChart3, 
  Layers,
  Search,
  Filter,
  RefreshCw
} from 'lucide-react';

import { usePriceData, useTokens, useMultiChainData } from '@/hooks/useApi';
import { 
  formatCurrency, 
  formatPercentage, 
  formatNumber,
  formatNetworkName,
  getProfitColor,
  NETWORK_COLORS,
  NETWORK_CHAIN_IDS
} from '@/lib/utils';
import { LoadingSpinner, SkeletonTable } from '@/components/ui/LoadingSpinner';

interface MultiChainPriceDashboardProps {
  detailed?: boolean;
}

interface NetworkCardProps {
  network: string;
  tokens: any[];
  isLoading?: boolean;
}

function NetworkCard({ network, tokens, isLoading }: NetworkCardProps) {
  const networkColor = NETWORK_COLORS[network.toLowerCase()] || '#64748b';
  
  if (isLoading) {
    return (
      <div className="glass-effect rounded-xl p-4">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-4 h-4 bg-dark-700 rounded-full animate-pulse"></div>
          <div className="h-5 bg-dark-700 rounded w-20 animate-pulse"></div>
        </div>
        <div className="space-y-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex justify-between items-center">
              <div className="h-4 bg-dark-700 rounded w-16 animate-pulse"></div>
              <div className="h-4 bg-dark-700 rounded w-20 animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const topTokens = tokens.slice(0, 5);
  const totalVolume = tokens.reduce((sum, token) => sum + (token.volume_24h || token.volume24h || 0), 0);

  return (
    <div className="glass-effect rounded-xl p-4 hover:bg-dark-700/30 transition-all duration-200">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div 
            className="w-4 h-4 rounded-full"
            style={{ backgroundColor: networkColor }}
          />
          <h4 className="font-semibold text-dark-100">
            {formatNetworkName(network)}
          </h4>
        </div>
        
        <div className="text-xs text-dark-400">
          {tokens.length} tokens
        </div>
      </div>

      <div className="space-y-3 mb-4">
        {topTokens.map((token, index) => {
          const price = token.price_usd || token.priceUsd || 0;
          const change = token.price_change_24h || token.priceChange24h || 0;
          
          return (
            <div key={token.symbol || index} className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-dark-200">
                  {token.symbol}
                </span>
                {token.is_verified && (
                  <div className="w-1.5 h-1.5 bg-success-500 rounded-full"></div>
                )}
              </div>
              
              <div className="text-right">
                <div className="text-sm font-medium text-dark-100">
                  {formatCurrency(price)}
                </div>
                <div className={`text-xs ${getProfitColor(change)}`}>
                  {change > 0 ? '+' : ''}{formatPercentage(change)}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="pt-3 border-t border-dark-700">
        <div className="flex justify-between items-center text-xs text-dark-400">
          <span>24h Volume</span>
          <span className="font-medium">{formatCurrency(totalVolume)}</span>
        </div>
      </div>
    </div>
  );
}

interface TokenRowProps {
  token: any;
  network: string;
}

function TokenRow({ token, network }: TokenRowProps) {
  const price = token.price_usd || token.priceUsd || 0;
  const change = token.price_change_24h || token.priceChange24h || 0;
  const volume = token.volume_24h || token.volume24h || 0;
  const marketCap = token.market_cap || token.marketCap || 0;
  const networkColor = NETWORK_COLORS[network.toLowerCase()] || '#64748b';

  return (
    <tr className="hover:bg-dark-700/30 transition-colors">
      <td className="px-6 py-4">
        <div className="flex items-center space-x-3">
          <div 
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: networkColor }}
          />
          <div>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-dark-100">{token.symbol}</span>
              {token.is_verified && (
                <div className="w-1.5 h-1.5 bg-success-500 rounded-full"></div>
              )}
            </div>
            <div className="text-sm text-dark-400">{token.name}</div>
          </div>
        </div>
      </td>
      
      <td className="px-6 py-4 text-dark-200">
        {formatNetworkName(network)}
      </td>
      
      <td className="px-6 py-4 font-medium text-dark-100">
        {formatCurrency(price)}
      </td>
      
      <td className={`px-6 py-4 font-medium ${getProfitColor(change)}`}>
        <div className="flex items-center space-x-1">
          {change > 0 ? (
            <TrendingUp className="w-3 h-3" />
          ) : change < 0 ? (
            <TrendingDown className="w-3 h-3" />
          ) : null}
          <span>{change > 0 ? '+' : ''}{formatPercentage(change)}</span>
        </div>
      </td>
      
      <td className="px-6 py-4 text-dark-200">
        {formatCurrency(volume)}
      </td>
      
      <td className="px-6 py-4 text-dark-200">
        {formatCurrency(marketCap)}
      </td>
      
      <td className="px-6 py-4">
        <div className={`px-2 py-1 rounded text-xs font-medium ${
          token.safety_score > 0.8 
            ? 'bg-success-500/20 text-success-400'
            : token.safety_score > 0.6
            ? 'bg-warning-500/20 text-warning-400'
            : 'bg-danger-500/20 text-danger-400'
        }`}>
          {((token.safety_score || 0) * 100).toFixed(0)}%
        </div>
      </td>
    </tr>
  );
}

export function MultiChainPriceDashboard({ detailed = false }: MultiChainPriceDashboardProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterNetwork, setFilterNetwork] = useState('all');
  const [sortBy, setSortBy] = useState('market_cap');

  // Fetch data for all supported networks
  const networks = ['ethereum', 'bsc', 'polygon', 'solana', 'avalanche', 'arbitrum', 'optimism', 'base', 'fantom', 'sui'];
  
  const { data: tokens, isLoading: tokensLoading } = useTokens({ limit: 50 });
  const { data: priceData, isLoading: priceLoading } = usePriceData({ 
    networks,
    timeframe: '24h'
  });
  const { data: multiChainData, isLoading: multiChainLoading } = useMultiChainData();

  const isLoading = tokensLoading || priceLoading || multiChainLoading;

  // Group tokens by network
  const tokensByNetwork = useMemo(() => {
    if (!tokens) return {};
    
    return tokens.reduce((acc, token) => {
      const network = token.network || 'ethereum';
      if (!acc[network]) acc[network] = [];
      acc[network].push(token);
      return acc;
    }, {} as Record<string, any[]>);
  }, [tokens]);

  // Filter and sort tokens for detailed view
  const filteredTokens = useMemo(() => {
    if (!tokens) return [];
    
    let filtered = tokens.filter(token => {
      const matchesSearch = searchTerm === '' || 
        token.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        token.name.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesNetwork = filterNetwork === 'all' || token.network === filterNetwork;
      
      return matchesSearch && matchesNetwork;
    });

    // Sort tokens
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'market_cap':
          return (b.market_cap || b.marketCap || 0) - (a.market_cap || a.marketCap || 0);
        case 'volume':
          return (b.volume_24h || b.volume24h || 0) - (a.volume_24h || a.volume24h || 0);
        case 'price':
          return (b.price_usd || b.priceUsd || 0) - (a.price_usd || a.priceUsd || 0);
        case 'change':
          return (b.price_change_24h || b.priceChange24h || 0) - (a.price_change_24h || a.priceChange24h || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [tokens, searchTerm, filterNetwork, sortBy]);

  if (isLoading) {
    return (
      <div className="glass-effect rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="h-6 bg-dark-700 rounded w-1/3 animate-pulse"></div>
          <div className="h-8 bg-dark-700 rounded w-24 animate-pulse"></div>
        </div>
        {detailed ? (
          <SkeletonTable rows={10} />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            {Array.from({ length: 10 }).map((_, i) => (
              <div key={i} className="glass-effect rounded-xl p-4 animate-pulse">
                <div className="h-4 bg-dark-700 rounded mb-4"></div>
                <div className="space-y-2">
                  {Array.from({ length: 3 }).map((_, j) => (
                    <div key={j} className="h-3 bg-dark-700 rounded"></div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  if (!detailed) {
    // Compact network overview
    return (
      <div className="glass-effect rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Layers className="w-6 h-6 text-primary-400" />
            <div>
              <h3 className="text-lg font-semibold text-dark-100">
                Multi-Chain Prices
              </h3>
              <p className="text-sm text-dark-400">
                Real-time prices across {networks.length} networks
              </p>
            </div>
          </div>
          
          <button className="btn-secondary text-sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
          {networks.map(network => (
            <NetworkCard
              key={network}
              network={network}
              tokens={tokensByNetwork[network] || []}
              isLoading={isLoading}
            />
          ))}
        </div>
      </div>
    );
  }

  // Detailed table view
  return (
    <div className="glass-effect rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <BarChart3 className="w-6 h-6 text-primary-400" />
          <div>
            <h3 className="text-lg font-semibold text-dark-100">
              Token Prices
            </h3>
            <p className="text-sm text-dark-400">
              {filteredTokens.length} tokens across {networks.length} networks
            </p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 flex flex-wrap items-center gap-4">
        <div className="flex-1 min-w-64">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-dark-400" />
            <input
              type="text"
              placeholder="Search tokens..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-field pl-10 w-full"
            />
          </div>
        </div>
        
        <select
          value={filterNetwork}
          onChange={(e) => setFilterNetwork(e.target.value)}
          className="input-field"
        >
          <option value="all">All Networks</option>
          {networks.map(network => (
            <option key={network} value={network}>
              {formatNetworkName(network)}
            </option>
          ))}
        </select>
        
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="input-field"
        >
          <option value="market_cap">Market Cap</option>
          <option value="volume">Volume</option>
          <option value="price">Price</option>
          <option value="change">24h Change</option>
        </select>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="data-table">
          <thead>
            <tr>
              <th>Token</th>
              <th>Network</th>
              <th>Price</th>
              <th>24h Change</th>
              <th>24h Volume</th>
              <th>Market Cap</th>
              <th>Safety Score</th>
            </tr>
          </thead>
          <tbody>
            {filteredTokens.map((token, index) => (
              <TokenRow
                key={`${token.symbol}-${token.network}-${index}`}
                token={token}
                network={token.network}
              />
            ))}
          </tbody>
        </table>
      </div>

      {filteredTokens.length === 0 && (
        <div className="text-center py-12">
          <DollarSign className="w-12 h-12 text-dark-600 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-dark-300 mb-2">
            No Tokens Found
          </h4>
          <p className="text-dark-500">
            No tokens match your current search and filter criteria.
          </p>
        </div>
      )}
    </div>
  );
}
