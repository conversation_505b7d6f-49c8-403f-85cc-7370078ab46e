'use client';

import { useState, useMemo } from 'react';
import { 
  Network, 
  Wifi, 
  WifiOff,
  Activity, 
  Clock, 
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Filter,
  RefreshCw,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

import { NetworkStatus } from '@/types';
import { 
  formatNumber, 
  formatTimeAgo,
  formatNetworkName,
  getStatusColor,
  NETWORK_COLORS,
  NETWORK_CHAIN_IDS
} from '@/lib/utils';
import { LoadingSpinner, SkeletonTable } from '@/components/ui/LoadingSpinner';

interface NetworkStatusPanelProps {
  networkStatus: NetworkStatus[];
  isLoading?: boolean;
  detailed?: boolean;
}

interface NetworkCardProps {
  network: NetworkStatus;
  detailed?: boolean;
}

function NetworkCard({ network, detailed = false }: NetworkCardProps) {
  const isHealthy = network.is_healthy || network.isHealthy;
  const blockNumber = network.block_number || network.blockNumber || 0;
  const gasPrice = network.gas_price || network.gasPrice || 0;
  const congestionLevel = network.congestion_level || network.congestionLevel || 'low';
  const lastUpdated = network.last_updated || network.lastUpdated || '';
  
  const networkColor = NETWORK_COLORS[network.network] || '#6B7280';
  const statusColor = isHealthy ? 'text-success-400' : 'text-error-400';
  const StatusIcon = isHealthy ? CheckCircle : XCircle;
  
  const congestionColors = {
    low: 'text-success-400',
    medium: 'text-warning-400',
    high: 'text-error-400',
  };
  
  const congestionColor = congestionColors[congestionLevel];

  return (
    <div className="glass-effect rounded-lg p-4 hover:bg-dark-700/30 transition-all duration-200">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div 
            className="w-4 h-4 rounded-full"
            style={{ backgroundColor: networkColor }}
          />
          <div>
            <h4 className="font-semibold text-dark-100">
              {formatNetworkName(network.network)}
            </h4>
            <div className="text-xs text-dark-400">
              Chain ID: {network.chainId || NETWORK_CHAIN_IDS[network.network] || 'Unknown'}
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <StatusIcon className={`w-4 h-4 ${statusColor}`} />
          <span className={`text-sm font-medium ${statusColor}`}>
            {isHealthy ? 'Healthy' : 'Unhealthy'}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-xs text-dark-500 mb-1">Block Height</div>
          <div className="text-sm font-medium text-dark-200">
            {formatNumber(blockNumber)}
          </div>
        </div>
        
        <div>
          <div className="text-xs text-dark-500 mb-1">Gas Price</div>
          <div className="text-sm font-medium text-dark-200">
            {gasPrice.toFixed(2)} Gwei
          </div>
        </div>
        
        <div>
          <div className="text-xs text-dark-500 mb-1">Latency</div>
          <div className="text-sm font-medium text-dark-200">
            {network.latency}ms
          </div>
        </div>
        
        <div>
          <div className="text-xs text-dark-500 mb-1">Congestion</div>
          <div className={`text-sm font-medium capitalize ${congestionColor}`}>
            {congestionLevel}
          </div>
        </div>
      </div>

      {detailed && (
        <div className="pt-4 border-t border-dark-700">
          <div className="grid grid-cols-1 gap-3">
            <div className="flex items-center justify-between">
              <span className="text-xs text-dark-500">RPC URL</span>
              <span className="text-xs text-dark-300 font-mono truncate max-w-32">
                {network.rpc_url || network.rpcUrl || 'N/A'}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-xs text-dark-500">Last Updated</span>
              <span className="text-xs text-dark-300">
                {formatTimeAgo(lastUpdated)}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

interface NetworkMetricsProps {
  networks: NetworkStatus[];
}

function NetworkMetrics({ networks }: NetworkMetricsProps) {
  const metrics = useMemo(() => {
    const total = networks.length;
    const healthy = networks.filter(n => n.is_healthy || n.isHealthy).length;
    const avgLatency = networks.length > 0 
      ? networks.reduce((sum, n) => sum + n.latency, 0) / networks.length 
      : 0;
    
    const congestionCounts = networks.reduce((acc, n) => {
      const level = n.congestion_level || n.congestionLevel || 'low';
      acc[level] = (acc[level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      healthy,
      unhealthy: total - healthy,
      healthyPercentage: total > 0 ? (healthy / total) * 100 : 0,
      avgLatency,
      congestionCounts,
    };
  }, [networks]);

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      <div className="metric-card">
        <div className="flex items-center space-x-2 mb-2">
          <Network className="w-4 h-4 text-primary-400" />
          <span className="text-sm font-medium text-dark-300">Total Networks</span>
        </div>
        <div className="text-xl font-bold text-dark-100">
          {metrics.total}
        </div>
      </div>

      <div className="metric-card">
        <div className="flex items-center space-x-2 mb-2">
          <CheckCircle className="w-4 h-4 text-success-400" />
          <span className="text-sm font-medium text-dark-300">Healthy</span>
        </div>
        <div className="text-xl font-bold text-success-400">
          {metrics.healthy}
        </div>
        <div className="text-xs text-dark-500">
          {metrics.healthyPercentage.toFixed(1)}%
        </div>
      </div>

      <div className="metric-card">
        <div className="flex items-center space-x-2 mb-2">
          <Clock className="w-4 h-4 text-warning-400" />
          <span className="text-sm font-medium text-dark-300">Avg Latency</span>
        </div>
        <div className="text-xl font-bold text-warning-400">
          {metrics.avgLatency.toFixed(0)}ms
        </div>
      </div>

      <div className="metric-card">
        <div className="flex items-center space-x-2 mb-2">
          <Activity className="w-4 h-4 text-error-400" />
          <span className="text-sm font-medium text-dark-300">High Congestion</span>
        </div>
        <div className="text-xl font-bold text-error-400">
          {metrics.congestionCounts.high || 0}
        </div>
      </div>
    </div>
  );
}

export function NetworkStatusPanel({ 
  networkStatus, 
  isLoading = false, 
  detailed = false 
}: NetworkStatusPanelProps) {
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterCongestion, setFilterCongestion] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('name');

  // Filter and sort networks
  const filteredNetworks = useMemo(() => {
    let filtered = [...networkStatus];

    // Apply status filter
    if (filterStatus === 'healthy') {
      filtered = filtered.filter(n => n.is_healthy || n.isHealthy);
    } else if (filterStatus === 'unhealthy') {
      filtered = filtered.filter(n => !(n.is_healthy || n.isHealthy));
    }

    // Apply congestion filter
    if (filterCongestion !== 'all') {
      filtered = filtered.filter(n => 
        (n.congestion_level || n.congestionLevel) === filterCongestion
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.network.localeCompare(b.network);
        case 'latency':
          return a.latency - b.latency;
        case 'block':
          return (b.block_number || b.blockNumber || 0) - (a.block_number || a.blockNumber || 0);
        case 'gas':
          return (b.gas_price || b.gasPrice || 0) - (a.gas_price || a.gasPrice || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [networkStatus, filterStatus, filterCongestion, sortBy]);

  if (isLoading) {
    return (
      <div className="glass-effect rounded-xl p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-6 h-6 bg-dark-700 rounded animate-pulse"></div>
          <div className="h-6 bg-dark-700 rounded w-32 animate-pulse"></div>
        </div>
        <SkeletonTable rows={5} />
      </div>
    );
  }

  return (
    <div className="glass-effect rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Network className="w-6 h-6 text-primary-400" />
          <div>
            <h3 className="text-lg font-semibold text-dark-100">
              Network Status
            </h3>
            <p className="text-sm text-dark-400">
              Real-time status across {networkStatus.length} blockchain networks
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-dark-400">Live Status</span>
        </div>
      </div>

      {/* Network Metrics */}
      {detailed && <NetworkMetrics networks={networkStatus} />}

      {/* Filters and Controls */}
      <div className="flex flex-wrap items-center gap-4 mb-6">
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-dark-400" />
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="input-field text-sm"
          >
            <option value="all">All Status</option>
            <option value="healthy">Healthy</option>
            <option value="unhealthy">Unhealthy</option>
          </select>
        </div>

        <select
          value={filterCongestion}
          onChange={(e) => setFilterCongestion(e.target.value)}
          className="input-field text-sm"
        >
          <option value="all">All Congestion</option>
          <option value="low">Low</option>
          <option value="medium">Medium</option>
          <option value="high">High</option>
        </select>

        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="input-field text-sm"
        >
          <option value="name">Network Name</option>
          <option value="latency">Latency</option>
          <option value="block">Block Height</option>
          <option value="gas">Gas Price</option>
        </select>

        <button className="btn-secondary text-sm ml-auto">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </button>
      </div>

      {/* Network Cards */}
      <div className="space-y-4">
        {filteredNetworks.length === 0 ? (
          <div className="text-center py-12">
            <Network className="w-12 h-12 text-dark-600 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-dark-300 mb-2">
              No Networks Found
            </h4>
            <p className="text-dark-500">
              {networkStatus.length === 0
                ? 'No network status data available.'
                : 'No networks match your current filters.'
              }
            </p>
          </div>
        ) : (
          <div className={`grid gap-4 ${
            detailed
              ? 'grid-cols-1 md:grid-cols-2'
              : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
          }`}>
            {filteredNetworks.map((network) => (
              <NetworkCard
                key={network.network}
                network={network}
                detailed={detailed}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
