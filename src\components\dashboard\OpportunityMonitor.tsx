'use client';

import { useState, useMemo } from 'react';
import { 
  TrendingUp, 
  Clock, 
  DollarSign, 
  Zap, 
  AlertTriangle, 
  CheckCircle,
  Filter,
  Search,
  ExternalLink
} from 'lucide-react';
import { FixedSizeList as List } from 'react-window';

import { ArbitrageOpportunity } from '@/types';
import { 
  formatCurrency, 
  formatTimeAgo, 
  formatNetworkName, 
  getStatusColor,
  getRiskColor,
  getConfidenceColor,
  getExplorerUrl,
  NETWORK_COLORS
} from '@/lib/utils';
import { LoadingSpinner, SkeletonTable } from '@/components/ui/LoadingSpinner';

interface OpportunityMonitorProps {
  opportunities: ArbitrageOpportunity[];
  isLoading?: boolean;
  detailed?: boolean;
}

interface OpportunityCardProps {
  opportunity: ArbitrageOpportunity;
  detailed?: boolean;
}

function OpportunityCard({ opportunity, detailed = false }: OpportunityCardProps) {
  const profit = opportunity.potential_profit || opportunity.potentialProfit || 0;
  const gasFees = opportunity.gas_fees || opportunity.gasFees || 0;
  const netProfit = profit - gasFees;
  const riskScore = opportunity.risk_score || opportunity.riskScore || 0;
  const confidence = opportunity.confidence || 0;

  const networkColor = NETWORK_COLORS[opportunity.network?.toLowerCase()] || '#64748b';

  return (
    <div className="glass-effect rounded-xl p-4 hover:bg-dark-700/50 transition-all duration-200 group">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div 
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: networkColor }}
          />
          <div>
            <h4 className="font-semibold text-dark-100 group-hover:text-white transition-colors">
              {opportunity.type.replace('-', ' ').toUpperCase()}
            </h4>
            <p className="text-sm text-dark-400">
              {formatNetworkName(opportunity.network)}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(opportunity.status)} bg-opacity-20`}>
            {opportunity.status.toUpperCase()}
          </div>
          {opportunity.mev_protection && (
            <div className="px-2 py-1 rounded text-xs font-medium bg-primary-500/20 text-primary-400">
              MEV
            </div>
          )}
        </div>
      </div>

      <div className="space-y-2 mb-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-dark-400">Assets:</span>
          <span className="text-sm font-medium text-dark-200">
            {opportunity.assets.join(' / ')}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-dark-400">Exchanges:</span>
          <span className="text-sm font-medium text-dark-200">
            {opportunity.exchanges.join(' & ')}
          </span>
        </div>
        
        {detailed && (
          <>
            <div className="flex items-center justify-between">
              <span className="text-sm text-dark-400">Risk Score:</span>
              <span className={`text-sm font-medium ${getRiskColor(riskScore)}`}>
                {(riskScore * 100).toFixed(1)}%
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-dark-400">Confidence:</span>
              <span className={`text-sm font-medium ${getConfidenceColor(confidence)}`}>
                {(confidence * 100).toFixed(1)}%
              </span>
            </div>
          </>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <p className="text-xs text-dark-400 mb-1">Gross Profit</p>
          <p className="text-lg font-bold text-success-400">
            {formatCurrency(profit)}
          </p>
        </div>
        
        <div className="text-center">
          <p className="text-xs text-dark-400 mb-1">Net Profit</p>
          <p className={`text-lg font-bold ${netProfit > 0 ? 'text-success-400' : 'text-danger-400'}`}>
            {formatCurrency(netProfit)}
          </p>
        </div>
      </div>

      <div className="flex items-center justify-between text-xs text-dark-500">
        <div className="flex items-center space-x-1">
          <Clock className="w-3 h-3" />
          <span>{formatTimeAgo(opportunity.created_at || opportunity.createdAt || '')}</span>
        </div>
        
        <div className="flex items-center space-x-1">
          <Zap className="w-3 h-3" />
          <span>Gas: {formatCurrency(gasFees)}</span>
        </div>
      </div>

      {detailed && opportunity.transaction_hash && (
        <div className="mt-3 pt-3 border-t border-dark-700">
          <a
            href={getExplorerUrl(opportunity.network, opportunity.transaction_hash)}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-1 text-xs text-primary-400 hover:text-primary-300"
          >
            <ExternalLink className="w-3 h-3" />
            <span>View on Explorer</span>
          </a>
        </div>
      )}
    </div>
  );
}

export function OpportunityMonitor({ 
  opportunities, 
  isLoading = false, 
  detailed = false 
}: OpportunityMonitorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterNetwork, setFilterNetwork] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [sortBy, setSortBy] = useState('profit');

  // Filter and sort opportunities
  const filteredOpportunities = useMemo(() => {
    let filtered = opportunities.filter(opp => {
      const matchesSearch = searchTerm === '' || 
        opp.assets.some(asset => asset.toLowerCase().includes(searchTerm.toLowerCase())) ||
        opp.exchanges.some(exchange => exchange.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesNetwork = filterNetwork === 'all' || opp.network === filterNetwork;
      const matchesType = filterType === 'all' || opp.type === filterType;
      
      return matchesSearch && matchesNetwork && matchesType;
    });

    // Sort opportunities
    filtered.sort((a, b) => {
      const profitA = (a.potential_profit || a.potentialProfit || 0) - (a.gas_fees || a.gasFees || 0);
      const profitB = (b.potential_profit || b.potentialProfit || 0) - (b.gas_fees || b.gasFees || 0);
      
      switch (sortBy) {
        case 'profit':
          return profitB - profitA;
        case 'risk':
          return (a.risk_score || a.riskScore || 0) - (b.risk_score || b.riskScore || 0);
        case 'time':
          return new Date(b.created_at || b.createdAt || '').getTime() - 
                 new Date(a.created_at || a.createdAt || '').getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [opportunities, searchTerm, filterNetwork, filterType, sortBy]);

  // Get unique networks and types for filters
  const networks = useMemo(() => 
    [...new Set(opportunities.map(opp => opp.network))], 
    [opportunities]
  );
  
  const types = useMemo(() => 
    [...new Set(opportunities.map(opp => opp.type))], 
    [opportunities]
  );

  if (isLoading) {
    return (
      <div className="glass-effect rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="h-6 bg-dark-700 rounded w-1/3 animate-pulse"></div>
          <div className="h-8 bg-dark-700 rounded w-24 animate-pulse"></div>
        </div>
        <SkeletonTable rows={5} />
      </div>
    );
  }

  return (
    <div className="glass-effect rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <TrendingUp className="w-6 h-6 text-success-400" />
          <div>
            <h3 className="text-lg font-semibold text-dark-100">
              Live Opportunities
            </h3>
            <p className="text-sm text-dark-400">
              {filteredOpportunities.length} active opportunities
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-dark-400">Real-time</span>
        </div>
      </div>

      {/* Filters and Search */}
      {detailed && (
        <div className="mb-6 space-y-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-dark-400" />
                <input
                  type="text"
                  placeholder="Search assets or exchanges..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input-field pl-10 w-full"
                />
              </div>
            </div>
            
            <select
              value={filterNetwork}
              onChange={(e) => setFilterNetwork(e.target.value)}
              className="input-field"
            >
              <option value="all">All Networks</option>
              {networks.map(network => (
                <option key={network} value={network}>
                  {formatNetworkName(network)}
                </option>
              ))}
            </select>
            
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="input-field"
            >
              <option value="all">All Types</option>
              {types.map(type => (
                <option key={type} value={type}>
                  {type.replace('-', ' ').toUpperCase()}
                </option>
              ))}
            </select>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="input-field"
            >
              <option value="profit">Sort by Profit</option>
              <option value="risk">Sort by Risk</option>
              <option value="time">Sort by Time</option>
            </select>
          </div>
        </div>
      )}

      {/* Opportunities List */}
      <div className="space-y-4">
        {filteredOpportunities.length === 0 ? (
          <div className="text-center py-12">
            <AlertTriangle className="w-12 h-12 text-dark-600 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-dark-300 mb-2">
              No Opportunities Found
            </h4>
            <p className="text-dark-500">
              {opportunities.length === 0 
                ? 'No arbitrage opportunities detected at the moment.'
                : 'No opportunities match your current filters.'
              }
            </p>
          </div>
        ) : (
          <div className={`grid gap-4 ${
            detailed 
              ? 'grid-cols-1' 
              : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
          }`}>
            {filteredOpportunities.map((opportunity) => (
              <OpportunityCard
                key={opportunity.id}
                opportunity={opportunity}
                detailed={detailed}
              />
            ))}
          </div>
        )}
      </div>

      {/* Summary Stats */}
      {filteredOpportunities.length > 0 && (
        <div className="mt-6 pt-6 border-t border-dark-700">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <p className="text-sm text-dark-400 mb-1">Total Opportunities</p>
              <p className="text-lg font-semibold text-dark-100">
                {filteredOpportunities.length}
              </p>
            </div>
            
            <div>
              <p className="text-sm text-dark-400 mb-1">Avg. Profit</p>
              <p className="text-lg font-semibold text-success-400">
                {formatCurrency(
                  filteredOpportunities.reduce((sum, opp) => 
                    sum + (opp.potential_profit || opp.potentialProfit || 0), 0
                  ) / filteredOpportunities.length
                )}
              </p>
            </div>
            
            <div>
              <p className="text-sm text-dark-400 mb-1">High Confidence</p>
              <p className="text-lg font-semibold text-primary-400">
                {filteredOpportunities.filter(opp => (opp.confidence || 0) > 0.8).length}
              </p>
            </div>
            
            <div>
              <p className="text-sm text-dark-400 mb-1">Low Risk</p>
              <p className="text-lg font-semibold text-warning-400">
                {filteredOpportunities.filter(opp => (opp.risk_score || opp.riskScore || 0) < 0.3).length}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
