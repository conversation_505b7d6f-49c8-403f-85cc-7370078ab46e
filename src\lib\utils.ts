import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Format currency values
export function formatCurrency(value: number, decimals: number = 2): string {
  if (value === 0) return '$0.00';
  
  const absValue = Math.abs(value);
  
  if (absValue >= 1e9) {
    return `$${(value / 1e9).toFixed(decimals)}B`;
  } else if (absValue >= 1e6) {
    return `$${(value / 1e6).toFixed(decimals)}M`;
  } else if (absValue >= 1e3) {
    return `$${(value / 1e3).toFixed(decimals)}K`;
  } else {
    return `$${value.toFixed(decimals)}`;
  }
}

// Format percentage values
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`;
}

// Format large numbers
export function formatNumber(value: number, decimals: number = 0): string {
  if (value === 0) return '0';
  
  const absValue = Math.abs(value);
  
  if (absValue >= 1e9) {
    return `${(value / 1e9).toFixed(decimals)}B`;
  } else if (absValue >= 1e6) {
    return `${(value / 1e6).toFixed(decimals)}M`;
  } else if (absValue >= 1e3) {
    return `${(value / 1e3).toFixed(decimals)}K`;
  } else {
    return value.toLocaleString();
  }
}

// Format time duration
export function formatDuration(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days}d ${hours % 24}h`;
  if (hours > 0) return `${hours}h ${minutes % 60}m`;
  if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
  return `${seconds}s`;
}

// Format time ago
export function formatTimeAgo(date: Date | string): string {
  const now = new Date();
  const targetDate = typeof date === 'string' ? new Date(date) : date;
  const diffMs = now.getTime() - targetDate.getTime();
  
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffSeconds < 60) return 'just now';
  if (diffMinutes < 60) return `${diffMinutes}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return targetDate.toLocaleDateString();
}

// Format network name
export function formatNetworkName(network: string): string {
  const networkNames: Record<string, string> = {
    'ethereum': 'Ethereum',
    'bsc': 'BSC',
    'polygon': 'Polygon',
    'solana': 'Solana',
    'avalanche': 'Avalanche',
    'arbitrum': 'Arbitrum',
    'optimism': 'Optimism',
    'base': 'Base',
    'fantom': 'Fantom',
    'sui': 'Sui',
  };
  
  return networkNames[network.toLowerCase()] || network;
}

// Get status color
export function getStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    'success': 'text-success-400',
    'completed': 'text-success-400',
    'healthy': 'text-success-400',
    'active': 'text-primary-400',
    'executing': 'text-warning-400',
    'processing': 'text-warning-400',
    'pending': 'text-warning-400',
    'queued': 'text-neutral',
    'failed': 'text-danger-400',
    'error': 'text-danger-400',
    'unhealthy': 'text-danger-400',
    'cancelled': 'text-neutral',
    'expired': 'text-neutral',
    'degraded': 'text-warning-400',
  };
  
  return statusColors[status.toLowerCase()] || 'text-neutral';
}

// Get profit/loss color
export function getProfitColor(value: number): string {
  if (value > 0) return 'text-success-400';
  if (value < 0) return 'text-danger-400';
  return 'text-neutral';
}

// Truncate address
export function truncateAddress(address: string, startLength: number = 6, endLength: number = 4): string {
  if (address.length <= startLength + endLength) return address;
  return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
}

// Validate Ethereum address
export function isValidAddress(address: string): boolean {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
}

// Calculate percentage change
export function calculatePercentageChange(current: number, previous: number): number {
  if (previous === 0) return 0;
  return ((current - previous) / previous) * 100;
}

// Debounce function
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle function
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Generate unique ID
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

// Sleep function
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Safe JSON parse
export function safeJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json);
  } catch {
    return fallback;
  }
}

// Format gas price
export function formatGasPrice(gwei: number): string {
  if (gwei < 1) return `${(gwei * 1000).toFixed(0)} mwei`;
  return `${gwei.toFixed(1)} gwei`;
}

// Calculate APY
export function calculateAPY(profit: number, principal: number, days: number): number {
  if (principal === 0 || days === 0) return 0;
  const dailyReturn = profit / principal / days;
  return (Math.pow(1 + dailyReturn, 365) - 1) * 100;
}

// Risk score to color
export function getRiskColor(riskScore: number): string {
  if (riskScore <= 0.3) return 'text-success-400';
  if (riskScore <= 0.6) return 'text-warning-400';
  return 'text-danger-400';
}

// Confidence score to color
export function getConfidenceColor(confidence: number): string {
  if (confidence >= 0.8) return 'text-success-400';
  if (confidence >= 0.6) return 'text-warning-400';
  return 'text-danger-400';
}

// Network chain IDs
export const NETWORK_CHAIN_IDS: Record<string, number> = {
  ethereum: 1,
  bsc: 56,
  polygon: 137,
  solana: 101, // Solana doesn't use EVM chain IDs, but we'll use this for consistency
  avalanche: 43114,
  arbitrum: 42161,
  optimism: 10,
  base: 8453,
  fantom: 250,
  sui: 101, // Sui doesn't use EVM chain IDs
};

// Network colors for UI
export const NETWORK_COLORS: Record<string, string> = {
  ethereum: '#627EEA',
  bsc: '#F3BA2F',
  polygon: '#8247E5',
  solana: '#00FFA3',
  avalanche: '#E84142',
  arbitrum: '#28A0F0',
  optimism: '#FF0420',
  base: '#0052FF',
  fantom: '#1969FF',
  sui: '#4DA2FF',
};

// Format blockchain explorer URL
export function getExplorerUrl(network: string, txHash: string): string {
  const explorers: Record<string, string> = {
    ethereum: 'https://etherscan.io/tx/',
    bsc: 'https://bscscan.com/tx/',
    polygon: 'https://polygonscan.com/tx/',
    solana: 'https://solscan.io/tx/',
    avalanche: 'https://snowtrace.io/tx/',
    arbitrum: 'https://arbiscan.io/tx/',
    optimism: 'https://optimistic.etherscan.io/tx/',
    base: 'https://basescan.org/tx/',
    fantom: 'https://ftmscan.com/tx/',
    sui: 'https://suiexplorer.com/txblock/',
  };

  return `${explorers[network.toLowerCase()] || '#'}${txHash}`;
}

// Local storage helpers
export const storage = {
  get: <T>(key: string, defaultValue: T): T => {
    if (typeof window === 'undefined') return defaultValue;
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch {
      return defaultValue;
    }
  },

  set: <T>(key: string, value: T): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
    }
  },

  remove: (key: string): void => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(key);
  },
};

// Performance monitoring
export function measurePerformance<T>(
  name: string,
  fn: () => T | Promise<T>
): T | Promise<T> {
  const start = performance.now();
  const result = fn();

  if (result instanceof Promise) {
    return result.finally(() => {
      const end = performance.now();
      console.log(`${name} took ${(end - start).toFixed(2)}ms`);
    });
  } else {
    const end = performance.now();
    console.log(`${name} took ${(end - start).toFixed(2)}ms`);
    return result;
  }
}
