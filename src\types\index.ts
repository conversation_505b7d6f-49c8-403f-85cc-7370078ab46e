// Core trading types
export interface ArbitrageOpportunity {
  id: string;
  type: 'intra-chain' | 'cross-chain' | 'flash-loan';
  network: string;
  sourceNetwork?: string;
  targetNetwork?: string;
  assets: string[];
  exchanges: string[];
  sourceExchange?: string;
  targetExchange?: string;
  potential_profit: number;
  potentialProfit?: number;
  gas_fees: number;
  gasFees?: number;
  confidence: number;
  risk_score: number;
  riskScore?: number;
  estimated_execution_time: number;
  estimatedExecutionTime?: number;
  created_at: string;
  createdAt?: string;
  expires_at: string;
  expiresAt?: string;
  status: 'active' | 'executing' | 'completed' | 'expired' | 'failed';
  validation_status?: 'pending' | 'validated' | 'failed';
  mev_protection?: boolean;
  flash_loan_required?: boolean;
  bridge_fees?: number;
  slippage_tolerance?: number;
}

export interface Trade {
  id: string;
  opportunity_id: string;
  opportunityId?: string;
  type: 'intra-chain' | 'cross-chain' | 'flash-loan';
  network: string;
  assets: string[];
  exchanges: string[];
  executed_profit: number;
  executedProfit?: number;
  gas_fees: number;
  gasFees?: number;
  bridge_fees?: number;
  bridgeFees?: number;
  total_fees: number;
  totalFees?: number;
  net_profit: number;
  netProfit?: number;
  execution_time: number;
  executionTime?: number;
  status: 'pending' | 'executing' | 'success' | 'failed' | 'cancelled';
  error_message?: string;
  errorMessage?: string;
  transaction_hash?: string;
  transactionHash?: string;
  block_number?: number;
  blockNumber?: number;
  created_at: string;
  createdAt?: string;
  completed_at?: string;
  completedAt?: string;
  mev_protection_used?: boolean;
  flash_loan_provider?: string;
  strategy_type?: string;
}

export interface Token {
  id: string;
  symbol: string;
  name: string;
  address: string;
  network: string;
  decimals: number;
  price_usd: number;
  priceUsd?: number;
  market_cap: number;
  marketCap?: number;
  volume_24h: number;
  volume24h?: number;
  price_change_24h: number;
  priceChange24h?: number;
  safety_score: number;
  safetyScore?: number;
  liquidity_score: number;
  liquidityScore?: number;
  is_verified: boolean;
  isVerified?: boolean;
  is_monitored: boolean;
  isMonitored?: boolean;
  last_updated: string;
  lastUpdated?: string;
}

export interface NetworkStatus {
  network: string;
  name: string;
  chainId: number;
  rpc_url: string;
  rpcUrl?: string;
  block_number: number;
  blockNumber?: number;
  gas_price: number;
  gasPrice?: number;
  is_healthy: boolean;
  isHealthy?: boolean;
  latency: number;
  congestion_level: 'low' | 'medium' | 'high';
  congestionLevel?: 'low' | 'medium' | 'high';
  last_updated: string;
  lastUpdated?: string;
}

export interface PerformanceMetrics {
  total_profit: number;
  totalProfit?: number;
  net_profit: number;
  netProfit?: number;
  total_trades: number;
  totalTrades?: number;
  successful_trades: number;
  successfulTrades?: number;
  failed_trades: number;
  failedTrades?: number;
  win_rate: number;
  winRate?: number;
  average_profit: number;
  averageProfit?: number;
  daily_volume: number;
  dailyVolume?: number;
  total_gas_fees: number;
  totalGasFees?: number;
  uptime: number;
  latency: number;
  cache_hit_ratio: number;
  cacheHitRatio?: number;
  error_rate: number;
  errorRate?: number;
}

export interface MLStrategy {
  id: string;
  strategy_type: string;
  strategyType?: string;
  network: string;
  success_rate: number;
  successRate?: number;
  total_executions: number;
  totalExecutions?: number;
  current_weight: number;
  currentWeight?: number;
  last_updated: string;
  lastUpdated?: string;
  performance_score: number;
  performanceScore?: number;
  risk_adjusted_return: number;
  riskAdjustedReturn?: number;
}

export interface LearningEvent {
  id: string;
  event_type: 'weight_update' | 'regime_change' | 'strategy_adaptation' | 'performance_alert';
  eventType?: string;
  strategy_type: string;
  strategyType?: string;
  network?: string;
  old_weight?: number;
  oldWeight?: number;
  new_weight?: number;
  newWeight?: number;
  reason: string;
  confidence: number;
  impact_score: number;
  impactScore?: number;
  created_at: string;
  createdAt?: string;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime: number;
  services: {
    [serviceName: string]: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      latency: number;
      last_check: string;
      lastCheck?: string;
      error_count: number;
      errorCount?: number;
    };
  };
  databases: {
    [dbName: string]: {
      isHealthy: boolean;
      latency: number;
      connection_count: number;
      connectionCount?: number;
      last_query: string;
      lastQuery?: string;
    };
  };
  networks: NetworkStatus[];
}

export interface FlashLoanQuote {
  provider: string;
  asset: string;
  amount: string;
  fee: number;
  fee_percentage: number;
  feePercentage?: number;
  available_liquidity: number;
  availableLiquidity?: number;
  execution_time: number;
  executionTime?: number;
  gas_estimate: number;
  gasEstimate?: number;
  is_available: boolean;
  isAvailable?: boolean;
}

export interface ExecutionQueue {
  id: string;
  opportunity_id: string;
  opportunityId?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  estimated_profit: number;
  estimatedProfit?: number;
  queue_position: number;
  queuePosition?: number;
  estimated_execution_time: number;
  estimatedExecutionTime?: number;
  created_at: string;
  createdAt?: string;
  started_at?: string;
  startedAt?: string;
  completed_at?: string;
  completedAt?: string;
}

// WebSocket message types
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
  id?: string;
  priority?: 'low' | 'medium' | 'high';
}

// API response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  timestamp: number;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Chart data types
export interface ChartDataPoint {
  timestamp: number;
  value: number;
  label?: string;
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    fill?: boolean;
  }[];
}

// Theme types
export type Theme = 'light' | 'dark';

// Component prop types
export interface DashboardProps {
  initialData?: {
    opportunities: ArbitrageOpportunity[];
    trades: Trade[];
    metrics: PerformanceMetrics;
    systemHealth: SystemHealth;
  };
}

export interface ComponentWithChildren {
  children: React.ReactNode;
}
